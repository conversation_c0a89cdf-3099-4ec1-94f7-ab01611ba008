package storage

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"

	"github.com/DataDog/zstd"
	"github.com/aws/aws-sdk-go-v2/aws"
	v4 "github.com/aws/aws-sdk-go-v2/aws/signer/v4"
	awshttp "github.com/aws/aws-sdk-go-v2/aws/transport/http"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/feature/s3/manager"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/aws/smithy-go/middleware"
	smithyhttp "github.com/aws/smithy-go/transport/http"
	"github.com/go-chi/httplog/v2"
	awstrace "gopkg.in/DataDog/dd-trace-go.v1/contrib/aws/aws-sdk-go-v2/aws"
	lsconfig "langchain.com/smith/config"
)

var (
	multipartMinSizeBytes int64 = 5 * 1024 * 1024
)

func NewS3StorageClient(keepAlive bool, opts *BlobStorageClientOptions) (*S3StorageClient, error) {
	// set keepAlive to false for testing to avoid goroutine leaks
	client := S3Client(keepAlive)
	uploader := manager.NewUploader(client, func(u *manager.Uploader) {
		u.ClientOptions = append(u.ClientOptions, s3.WithAPIOptions(
			// https://aws.github.io/aws-sdk-go-v2/docs/sdk-utilities/s3/#unseekable-streaming-input
			v4.SwapComputePayloadSHA256ForUnsignedPayloadMiddleware,
		))
	})
	if uploader == nil {
		return nil, errors.New("failed to create S3 client")
	}
	return &S3StorageClient{
		S3:          client,
		uploader:    uploader,
		baseStorage: *NewBaseStorage(opts),
	}, nil
}

type S3StorageClient struct {
	S3       *s3.Client
	uploader *manager.Uploader
	baseStorage
}

func (c *S3StorageClient) HealthCheck(ctx context.Context, input *HealthCheckInput) error {
	for _, bucket := range input.Buckets {
		if bucket == "" {
			continue
		}
		_, err := c.S3.HeadBucket(ctx, &s3.HeadBucketInput{
			Bucket: &bucket,
		})
		if err != nil {
			return err
		}
	}
	return nil
}

func (c *S3StorageClient) CopyObject(ctx context.Context, input *CopyObjectInput) error {
	c.baseStorage.failIfDisabled()
	source := url.QueryEscape(fmt.Sprintf("%s/%s", input.SourceBucket, input.SourceKey))
	_, err := c.S3.CopyObject(ctx, &s3.CopyObjectInput{
		Bucket:     &input.DestBucket,
		CopySource: &source,
		Key:        &input.DestKey,
	})
	return err
}

func (c *S3StorageClient) GetObject(ctx context.Context, input *GetObjectInput) (*GetObjectOutput, error) {
	c.baseStorage.failIfDisabled()
	getObjectInput := &s3.GetObjectInput{
		Bucket: &input.Bucket,
		Key:    &input.Key,
	}

	// Add Range header if specified
	if input.Range != nil {
		rangeHeader := fmt.Sprintf("bytes=%d-%d", input.Range.Start, input.Range.End-1)
		getObjectInput.Range = &rangeHeader
	}

	resp, err := c.S3.GetObject(ctx, getObjectInput, func(o *s3.Options) {
		// forward accept-encoding header
		if input.ContentEncoding != "" {
			o.APIOptions = append(o.APIOptions,
				smithyhttp.AddHeaderValue("Accept-Encoding", input.ContentEncoding))
		}
	})
	if err != nil {
		var notFound *types.NoSuchKey
		if errors.As(err, &notFound) {
			return nil, ErrNotFound
		}
		return nil, err
	}

	if resp.ContentEncoding != nil && *resp.ContentEncoding == zstdContentEncoding && !input.RawResponse {
		dec := zstd.NewReader(resp.Body)
		resp.Body = dec
		resp.ContentEncoding = nil
		resp.ContentLength = nil
	} else if input.RawResponse && resp.ContentEncoding != nil && *resp.ContentEncoding == zstdContentEncoding {
		resp.ContentEncoding = nil
	}

	return &GetObjectOutput{
		Body:            resp.Body,
		ContentEncoding: resp.ContentEncoding,
		ContentLength:   resp.ContentLength,
		ContentType:     resp.ContentType,
		LastModified:    resp.LastModified,
	}, nil
}

func (c *S3StorageClient) HeadObject(ctx context.Context, input *HeadObjectInput) (*HeadObjectOutput, error) {
	c.baseStorage.failIfDisabled()
	resp, err := c.S3.HeadObject(ctx, &s3.HeadObjectInput{
		Bucket: &input.Bucket,
		Key:    &input.Key,
	}, func(o *s3.Options) {
		// forward accept-encoding header
		if input.ContentEncoding != "" {
			o.APIOptions = append(o.APIOptions,
				smithyhttp.AddHeaderValue("Accept-Encoding", input.ContentEncoding))
		}
	})
	if err != nil {
		var notFound *types.NotFound
		if errors.As(err, &notFound) {
			return nil, ErrNotFound
		}
		return nil, err
	}
	return &HeadObjectOutput{
		ContentType:     resp.ContentType,
		ContentEncoding: resp.ContentEncoding,
		ContentLength:   resp.ContentLength,
		LastModified:    resp.LastModified,
	}, nil
}

func (c *S3StorageClient) doUpload(ctx context.Context, input *UploadObjectInput, reader io.Reader) error {
	var (
		err  error
		body io.Reader
	)

	enc := strings.TrimSpace(input.ContentEncoding)

	contentLength := input.ContentLength

	if input.ContentLength > 0 && input.ContentLength <= multipartMinSizeBytes {
		// If object is small enough, buffer it so we can set Content-Length
		if enc == "" {
			enc = zstdContentEncoding

			var buf bytes.Buffer
			zw := zstd.NewWriter(&buf)
			if _, err = io.Copy(zw, reader); err != nil {
				zw.Close()
				return err
			}
			zw.Close()
			contentLength = int64(buf.Len())
			body = bytes.NewReader(buf.Bytes())
		} else {
			body = reader
		}

		_, err = c.S3.PutObject(ctx, &s3.PutObjectInput{
			Bucket:          &input.Bucket,
			Key:             &input.Key,
			Body:            body,
			ContentType:     &input.ContentType,
			ContentEncoding: &enc,
			ContentLength:   &contentLength,
		}, s3.WithAPIOptions(v4.SwapComputePayloadSHA256ForUnsignedPayloadMiddleware))
		return err
	}

	if enc == "" {
		enc = zstdContentEncoding

		pr, pw := io.Pipe()
		go func() {
			zw := zstd.NewWriter(pw)
			_, cErr := io.Copy(zw, reader)
			zw.Close()
			_ = pw.CloseWithError(cErr)
		}()
		body = pr
	} else {
		body = reader
	}

	putObj := &s3.PutObjectInput{
		Bucket:          &input.Bucket,
		Key:             &input.Key,
		Body:            body,
		ContentType:     &input.ContentType,
		ContentEncoding: &enc,
	}

	if enc != zstdContentEncoding && input.ContentLength > 0 {
		putObj.ContentLength = &input.ContentLength
	}

	_, err = c.uploader.Upload(ctx, putObj)
	return err
}

func (c *S3StorageClient) UploadObject(ctx context.Context, input *UploadObjectInput) (UploadStrategy, error) {
	c.baseStorage.failIfDisabled()
	oplog := httplog.LogEntry(ctx)
	reader, cleanup, strategy, err := c.baseStorage.prepareReader(ctx, input.Reader, input.ContentLength, oplog)
	defer cleanup()
	if err != nil {
		return strategy, err
	}

	err = c.doUpload(ctx, input, reader)
	return strategy, err
}

func (c *S3StorageClient) UploadObjectAsync(ctx context.Context, input *UploadObjectInput) (*UploadAsyncResult, error) {
	c.baseStorage.failIfDisabled()
	oplog := httplog.LogEntry(ctx)
	reader, cleanup, strategy, err := c.baseStorage.prepareReader(ctx, input.Reader, input.ContentLength, oplog)
	if err != nil {
		return nil, err
	}

	if strategy == UploadStrategyDirect {
		// upload directly without offloading to a goroutine
		// this is to avoid data races when the reader is closed before the upload completes
		defer cleanup()
		err := c.doUpload(ctx, input, reader)
		errChan := make(chan error, 1)
		errChan <- err
		return &UploadAsyncResult{
			Strategy: strategy,
			ErrChan:  errChan,
		}, nil
	}

	errChan := make(chan error, 1)
	go func() {
		defer cleanup()
		errChan <- c.doUpload(ctx, input, reader)
	}()

	return &UploadAsyncResult{
		Strategy: strategy,
		ErrChan:  errChan,
	}, nil
}

func S3Client(keepAlive bool) *s3.Client {
	// create config from env vars
	var (
		cfg  aws.Config
		err  error
		http = awshttp.NewBuildableClient().WithTransportOptions(func(tr *http.Transport) {
			tr.MaxIdleConnsPerHost = 10
			tr.DisableKeepAlives = !keepAlive
		})
	)
	if lsconfig.Env.S3AccessKey != "" && lsconfig.Env.S3AccessKeySecret != "" {
		cfg = aws.Config{
			Region:           lsconfig.Env.AWSRegion,
			RetryMaxAttempts: 5,
			RetryMode:        "standard",
			Credentials: credentials.NewStaticCredentialsProvider(
				lsconfig.Env.S3AccessKey, lsconfig.Env.S3AccessKeySecret, ""),
			HTTPClient: http,
		}
	} else {
		if lsconfig.Env.S3Profile != "" {
			cfg, err = config.LoadDefaultConfig(context.Background(), config.WithSharedConfigProfile(lsconfig.Env.S3Profile), config.WithHTTPClient(http))
			if err != nil {
				panic(err)
			}
		} else {
			cfg, err = config.LoadDefaultConfig(context.Background(), config.WithHTTPClient(http), config.WithRetryMode("standard"), config.WithRetryMaxAttempts(5))
		}
		if err != nil {
			panic(err)
		}
	}
	if lsconfig.Env.DatadogEnabled && !lsconfig.Env.DatadogS3Disabled {
		awstrace.AppendMiddleware(&cfg)
	}
	client := s3.NewFromConfig(cfg, func(o *s3.Options) {
		o.BaseEndpoint = aws.String(lsconfig.Env.S3ApiUrl)
		// GCS alters the Accept-Encoding header, which breaks the v2 request signature
		// (https://github.com/aws/aws-sdk-go-v2/issues/1816)
		if strings.Contains(lsconfig.Env.S3ApiUrl, "storage.googleapis.com") {
			ignoreSigningHeaders(o, []string{"Accept-Encoding"})
		}
	})
	return client
}

// ignoreSigningHeaders excludes the listed headers
// from the request signature because some providers may alter them.
//
// See https://github.com/aws/aws-sdk-go-v2/issues/1816.
func ignoreSigningHeaders(o *s3.Options, headers []string) {
	o.APIOptions = append(o.APIOptions, func(stack *middleware.Stack) error {
		if err := stack.Finalize.Insert(ignoreHeaders(headers), "Signing", middleware.Before); err != nil {
			return err
		}

		if err := stack.Finalize.Insert(restoreIgnored(), "Signing", middleware.After); err != nil {
			return err
		}

		return nil
	})
}

type ignoredHeadersKey struct{}

func ignoreHeaders(headers []string) middleware.FinalizeMiddleware {
	return middleware.FinalizeMiddlewareFunc(
		"IgnoreHeaders",
		func(ctx context.Context, in middleware.FinalizeInput, next middleware.FinalizeHandler) (out middleware.FinalizeOutput, metadata middleware.Metadata, err error) {
			req, ok := in.Request.(*smithyhttp.Request)
			if !ok {
				return out, metadata, &v4.SigningError{Err: fmt.Errorf("(ignoreHeaders) unexpected request middleware type %T", in.Request)}
			}

			ignored := make(map[string]string, len(headers))
			for _, h := range headers {
				ignored[h] = req.Header.Get(h)
				req.Header.Del(h)
			}

			ctx = middleware.WithStackValue(ctx, ignoredHeadersKey{}, ignored)

			return next.HandleFinalize(ctx, in)
		},
	)
}

func restoreIgnored() middleware.FinalizeMiddleware {
	return middleware.FinalizeMiddlewareFunc(
		"RestoreIgnored",
		func(ctx context.Context, in middleware.FinalizeInput, next middleware.FinalizeHandler) (out middleware.FinalizeOutput, metadata middleware.Metadata, err error) {
			req, ok := in.Request.(*smithyhttp.Request)
			if !ok {
				return out, metadata, &v4.SigningError{Err: fmt.Errorf("(restoreIgnored) unexpected request middleware type %T", in.Request)}
			}

			ignored, _ := middleware.GetStackValue(ctx, ignoredHeadersKey{}).(map[string]string)
			for k, v := range ignored {
				req.Header.Set(k, v)
			}

			return next.HandleFinalize(ctx, in)
		},
	)
}
