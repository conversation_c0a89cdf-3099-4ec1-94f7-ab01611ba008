package storage_test

import (
	"bytes"
	"context"
	"crypto/rand"
	"fmt"
	"io"
	"testing"
	"time"

	"langchain.com/smith/storage"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"langchain.com/smith/config"
	"langchain.com/smith/testutil/leak"
)

var testBucketName = config.Env.S3BucketName

// setupS3Client creates an S3 client connected to the local MinIO instance
func setupS3Client(t *testing.T) *storage.S3StorageClient {
	s3StorageClient, err := storage.NewS3StorageClient(false, nil)
	require.NoError(t, err, "Failed to create S3 storage client")
	return s3StorageClient
}

// generateRandomBytes creates random data of specified size
func generateRandomBytes(size int) ([]byte, error) {
	data := make([]byte, size)
	_, err := rand.Read(data)
	if err != nil {
		return nil, err
	}
	return data, nil
}

// TestContinuousUploader_UploadReader tests the ContinuousUploader with various data sizes
func TestContinuousUploader_UploadReader(t *testing.T) {
	client := setupS3Client(t)
	ctx := context.Background()
	defer leak.VerifyNoLeak(t)

	testCases := []struct {
		name                   string
		dataSize               int
		partSize               int64
		concurrency            int
		numChunks              int // Number of chunks to split the data into (number of requests to UploadReader)
		expectedUploadStrategy storage.Strategy
		expectedNumParts       int
		addDelay               bool
	}{
		{
			name:                   "Tiny upload (single part)",
			dataSize:               24 * 1024,       // 24 KiB
			partSize:               5 * 1024 * 1024, // 5 MiB
			concurrency:            3,
			numChunks:              3,
			expectedUploadStrategy: storage.UploadStrategySinglePart,
			expectedNumParts:       1,
		},
		{
			name:                   "Small upload (single part)",
			dataSize:               1 * 1024 * 1024, // 1 MiB
			partSize:               5 * 1024 * 1024, // 5 MiB
			concurrency:            3,
			numChunks:              3,
			expectedUploadStrategy: storage.UploadStrategySinglePart,
			expectedNumParts:       1,
		},
		{
			name:                   "Medium upload (single part)",
			dataSize:               4 * 1024 * 1024, // 4 MiB
			partSize:               5 * 1024 * 1024, // 5 MiB
			concurrency:            3,
			numChunks:              3,
			expectedUploadStrategy: storage.UploadStrategySinglePart,
			expectedNumParts:       1,
		},
		{
			name:                   "Large upload (multi-part)",
			dataSize:               12 * 1024 * 1024, // 12 MiB
			partSize:               5 * 1024 * 1024,  // 5 MiB
			concurrency:            3,
			numChunks:              3,
			expectedUploadStrategy: storage.UploadStrategyMultipart,
			expectedNumParts:       3,
		},
		{
			name:                   "Large upload (multi-part, highly chunked)",
			dataSize:               16 * 1024 * 1024, // 12 MiB
			partSize:               5 * 1024 * 1024,  // 5 MiB
			concurrency:            5,
			numChunks:              50,
			expectedUploadStrategy: storage.UploadStrategyMultipart,
			expectedNumParts:       4,
		},
		{
			name:                   "Large upload (multi-part, highly chunked with delay)",
			dataSize:               12 * 1024 * 1024, // 12 MiB
			partSize:               5 * 1024 * 1024,  // 5 MiB
			concurrency:            5,
			numChunks:              5,
			expectedUploadStrategy: storage.UploadStrategyMultipart,
			expectedNumParts:       3,
			addDelay:               true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Generate test data
			data, err := generateRandomBytes(tc.dataSize)
			require.NoError(t, err, "Failed to generate test data")

			// Create object key for this test
			objectKey := fmt.Sprintf("test-upload-%s.dat", tc.name)

			// Create uploader with specified part size and concurrency
			uploader, err := storage.NewContinuousUploader(
				ctx,
				client.S3,
				testBucketName,
				storage.WithPartSize(tc.partSize),
				storage.WithConcurrency(tc.concurrency),
			)
			defer uploader.Close()
			require.NoError(t, err, "Failed to create uploader")

			// Start upload
			uploader.StartUpload(objectKey)

			// Upload data in smaller chunks to simulate real usage
			chunkSize := tc.dataSize / tc.numChunks

			type chunkInfo struct {
				chunk       []byte
				startOffset int64
				endOffset   int64
			}
			chunks := make([]chunkInfo, tc.numChunks)

			for i := 0; i < tc.numChunks; i++ {
				start := i * chunkSize
				end := start + chunkSize
				if i == tc.numChunks-1 {
					end = tc.dataSize // Make sure last chunk gets the remainder
				}

				chunk := data[start:end]
				reader := bytes.NewReader(chunk)
				startOffset, endOffset, err := uploader.UploadReader(reader)
				assert.NoError(t, err, "Failed to upload chunk %d", i+1)

				// Store chunk info for later verification
				chunks[i] = chunkInfo{
					chunk:       chunk,
					startOffset: startOffset,
					endOffset:   endOffset,
				}

				if tc.addDelay {
					time.Sleep(75 * time.Millisecond)
				}
			}

			// Complete the upload and get part information
			completionResult, err := uploader.Complete()
			assert.NoError(t, err, "Failed to complete upload")

			// Verify part information
			assert.Equal(t, tc.expectedUploadStrategy, completionResult.UploadStrategy, "Upload strategy mismatch")
			assert.Equal(t, tc.expectedNumParts, completionResult.NumParts, "Number of parts mismatch")

			// Verify the uploaded object
			_, err = client.HeadObject(context.Background(), &storage.HeadObjectInput{
				Bucket: testBucketName,
				Key:    objectKey,
			})
			assert.NoError(t, err, "Failed to verify uploaded object")

			// Verify each chunk individually using the stored offsets
			for i, chunk := range chunks {

				getChunkObj, err := client.GetObject(context.Background(), &storage.GetObjectInput{
					Bucket: testBucketName,
					Key:    objectKey,
					Range:  &storage.Range{Start: chunk.startOffset, End: chunk.endOffset},
				})
				assert.NoError(t, err, "Failed to get chunk %d from S3", i+1)

				chunkData, err := io.ReadAll(getChunkObj.Body)
				assert.NoError(t, err, "Failed to read chunk %d data", i+1)
				defer getChunkObj.Body.Close()

				// Verify the chunk size and content
				assert.Equal(t, len(chunk.chunk), len(chunkData), "Chunk %d size mismatch", i+1)
				assert.Equal(t, chunk.chunk, chunkData, "Chunk %d content mismatch", i+1)
			}

			// Delete the object
			_, err = client.S3.DeleteObject(context.Background(), &s3.DeleteObjectInput{
				Bucket: aws.String(testBucketName),
				Key:    aws.String(objectKey),
			})
			assert.NoError(t, err, "Failed to delete uploaded object")
		})
	}
}

// TestContinuousUploader_Options tests the functional options
func TestContinuousUploader_Options(t *testing.T) {
	client := setupS3Client(t)
	ctx := context.Background()
	defer leak.VerifyNoLeak(t)

	// Test invalid part size
	_, err := storage.NewContinuousUploader(
		ctx,
		client.S3,
		testBucketName,
		storage.WithPartSize(1*1024*1024), // 1 MiB (too small)
	)
	assert.Error(t, err, "Expected error for too small part size")
	assert.Contains(t, err.Error(), "part size must be at least 5 MiB")

	// Test invalid concurrency
	_, err = storage.NewContinuousUploader(
		ctx,
		client.S3,
		testBucketName,
		storage.WithConcurrency(0), // Invalid
	)
	assert.Error(t, err, "Expected error for invalid concurrency")
	assert.Contains(t, err.Error(), "concurrency must be at least 1")

	// Test negative spool limit
	_, err = storage.NewContinuousUploader(
		ctx,
		client.S3,
		testBucketName,
		storage.WithSpoolLimitBytes(-1), // Invalid
	)
	assert.Error(t, err, "Expected error for negative spool limit")
	assert.Contains(t, err.Error(), "spool limit")

	// Test valid options
	cu, err := storage.NewContinuousUploader(
		ctx,
		client.S3,
		testBucketName,
		storage.WithPartSize(10*1024*1024),
		storage.WithConcurrency(8),
		storage.WithSpoolLimitBytes(20*1024*1024), // 20 MiB spool limit
	)
	assert.NoError(t, err, "Failed to create uploader with valid options")
	cu.Close()
}

// TestContinuousUploader_StateManagement tests the state transitions and validation
// to ensure the uploader's methods are called in the correct order.
func TestContinuousUploader_StateManagement(t *testing.T) {
	client := setupS3Client(t)
	ctx := context.Background()
	defer leak.VerifyNoLeak(t)

	t.Run("correct usage sequence", func(t *testing.T) {
		// Create a new uploader (starts in stateNotStarted)
		uploader, err := storage.NewContinuousUploader(ctx, client.S3, testBucketName)
		require.NoError(t, err, "Failed to create uploader")
		defer uploader.Close()

		// 1. Start upload (transitions to stateInProgress)
		objectKey := "test-state-correct-sequence.dat"
		err = uploader.StartUpload(objectKey)
		require.NoError(t, err, "StartUpload should succeed")

		// 2. Upload data (should work in stateInProgress)
		data := []byte("small test data")
		reader := bytes.NewReader(data)
		_, _, err = uploader.UploadReader(reader)
		require.NoError(t, err, "UploadReader should succeed when in stateInProgress")

		// 3. Complete (transitions to stateCompleted)
		result, err := uploader.Complete()
		require.NoError(t, err, "Complete should succeed when in stateInProgress")
		assert.Equal(t, storage.UploadStrategySinglePart, result.UploadStrategy)
	})

	t.Run("calling StartUpload twice", func(t *testing.T) {
		uploader, err := storage.NewContinuousUploader(ctx, client.S3, testBucketName)
		require.NoError(t, err, "Failed to create uploader")
		defer uploader.Close()

		// First StartUpload should succeed
		err = uploader.StartUpload("test-double-start-1.dat")
		require.NoError(t, err, "First StartUpload should succeed")

		// Second StartUpload should fail
		err = uploader.StartUpload("test-double-start-2.dat")
		require.Error(t, err, "Second StartUpload should fail")
		assert.Contains(t, err.Error(), "current state", "Error should reference the state")
	})

	t.Run("calling UploadReader before StartUpload", func(t *testing.T) {
		uploader, err := storage.NewContinuousUploader(ctx, client.S3, testBucketName)
		require.NoError(t, err, "Failed to create uploader")
		defer uploader.Close()

		// UploadReader before StartUpload should fail
		data := []byte("test data")
		reader := bytes.NewReader(data)
		_, _, err = uploader.UploadReader(reader)
		require.Error(t, err, "UploadReader should fail before StartUpload")
		assert.Contains(t, err.Error(), "current state", "Error should reference the state")
	})

	t.Run("calling Complete before StartUpload", func(t *testing.T) {
		uploader, err := storage.NewContinuousUploader(ctx, client.S3, testBucketName)
		require.NoError(t, err, "Failed to create uploader")
		defer uploader.Close()

		// Complete before StartUpload should fail
		_, err = uploader.Complete()
		require.Error(t, err, "Complete should fail before StartUpload")
		assert.Contains(t, err.Error(), "current state", "Error should reference the state")
	})

	t.Run("calling UploadReader after Complete", func(t *testing.T) {
		uploader, err := storage.NewContinuousUploader(ctx, client.S3, testBucketName)
		require.NoError(t, err, "Failed to create uploader")
		defer uploader.Close()

		// Do a minimal valid upload sequence
		err = uploader.StartUpload("test-upload-after-complete.dat")
		require.NoError(t, err)

		data := []byte("small test data")
		reader := bytes.NewReader(data)
		_, _, err = uploader.UploadReader(reader)
		require.NoError(t, err)

		// Complete the upload
		_, err = uploader.Complete()
		require.NoError(t, err)

		// Try to upload more data after completion
		reader = bytes.NewReader([]byte("more data"))
		_, _, err = uploader.UploadReader(reader)
		require.Error(t, err, "UploadReader should fail after Complete")
		assert.Contains(t, err.Error(), "current state", "Error should reference the state")
	})

	t.Run("calling UploadReader after Close", func(t *testing.T) {
		uploader, err := storage.NewContinuousUploader(ctx, client.S3, testBucketName)
		require.NoError(t, err, "Failed to create uploader")

		// Do a minimal valid upload sequence
		err = uploader.StartUpload("test-upload-after-close.dat")
		require.NoError(t, err)

		// Close the uploader
		uploader.Close()

		// Try to upload more data after Close
		data := []byte("more data")
		reader := bytes.NewReader(data)
		_, _, err = uploader.UploadReader(reader)
		require.Error(t, err, "UploadReader should fail after Close")
		assert.Contains(t, err.Error(), "current state", "Error should reference the state")
	})

	t.Run("calling Complete twice", func(t *testing.T) {
		uploader, err := storage.NewContinuousUploader(ctx, client.S3, testBucketName)
		require.NoError(t, err, "Failed to create uploader")
		defer uploader.Close()

		// Do a minimal valid upload sequence
		err = uploader.StartUpload("test-double-complete.dat")
		require.NoError(t, err)

		data := []byte("small test data")
		reader := bytes.NewReader(data)
		_, _, err = uploader.UploadReader(reader)
		require.NoError(t, err)

		// First Complete should succeed
		_, err = uploader.Complete()
		require.NoError(t, err)

		// Second Complete should fail
		_, err = uploader.Complete()
		require.Error(t, err, "Second Complete should fail")
		assert.Contains(t, err.Error(), "current state", "Error should reference the state")
	})

	t.Run("calling Close multiple times", func(t *testing.T) {
		uploader, err := storage.NewContinuousUploader(ctx, client.S3, testBucketName)
		require.NoError(t, err, "Failed to create uploader")

		// Close the uploader multiple times
		uploader.Close()
		uploader.Close()
	})

	t.Run("Close aborts multipart upload", func(t *testing.T) {
		uploader, err := storage.NewContinuousUploader(ctx, client.S3, testBucketName,
			storage.WithPartSize(5*1024*1024), // 5MB part size
		)
		require.NoError(t, err, "Failed to create uploader")

		// Start an upload
		objectKey := "test-close-aborts-multipart.dat"
		err = uploader.StartUpload(objectKey)
		require.NoError(t, err)

		data, err := generateRandomBytes(6 * 1024 * 1024) // 6MB data to trigger multipart upload
		require.NoError(t, err, "Failed to generate test data")

		reader := bytes.NewReader(data)
		_, _, err = uploader.UploadReader(reader)
		require.NoError(t, err)

		// Call Close before Complete which should abort the upload
		uploader.Close()

		// Verify the uploader can't be used anymore
		reader = bytes.NewReader([]byte("more data"))
		_, _, err = uploader.UploadReader(reader)
		require.Error(t, err, "UploadReader should fail after Close")
		assert.Contains(t, err.Error(), "current state", "Error should reference the state")

		// Verify the object doesn't exist in S3 (the upload was aborted)
		_, err = client.HeadObject(context.Background(), &storage.HeadObjectInput{
			Bucket: testBucketName,
			Key:    objectKey,
		})

		assert.Error(t, err, "Object should not exist in S3 after aborting multipart upload")
	})
}

// TestContinuousUploader_OutOfDisk tests that the uploader correctly fails when the spool limit is exceeded
func TestContinuousUploader_OutOfDisk(t *testing.T) {
	client := setupS3Client(t)
	ctx := context.Background()
	defer leak.VerifyNoLeak(t)

	// Create uploader with very small spool limit (100 bytes)
	uploader, err := storage.NewContinuousUploader(
		ctx,
		client.S3,
		testBucketName,
		storage.WithSpoolLimitBytes(100),
	)
	require.NoError(t, err, "Failed to create uploader with low spool limit")
	defer uploader.Close()

	// Start upload
	objectKey := "test-out-of-disk.dat"
	err = uploader.StartUpload(objectKey)
	require.NoError(t, err, "Failed to start upload")

	data, err := generateRandomBytes(200)
	require.NoError(t, err, "Failed to generate test data")

	// Upload data should fail due to spool limit
	reader := bytes.NewReader(data)
	_, _, err = uploader.UploadReader(reader)
	require.Error(t, err, "Expected error when exceeding spool limit")
	assert.Contains(t, err.Error(), "cannot spool to disk: exceeds limit of 100 bytes", "Expected specific error message")
}
