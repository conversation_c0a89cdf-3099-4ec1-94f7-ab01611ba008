package main

import (
	"bytes"
	"fmt"
	"log"

	"github.com/DataDog/zstd"
)

func main() {
	data := []byte("hello world, this is a test of zstd compression")

	// Case 1: Flush but don't Close
	var buf1 bytes.Buffer
	zw1 := zstd.NewWriter(&buf1)
	_, err := zw1.Write(data)
	if err != nil {
		log.Fatalf("write (flush case): %v", err)
	}
	err = zw1.Flush()
	if err != nil {
		log.Fatalf("flush: %v", err)
	}
	// zw1.Close() is intentionally omitted

	// Attempt to decompress (should fail)
	fmt.Println("Trying to decompress flushed-but-not-closed data:")
	_, err = zstd.Decompress(nil, buf1.Bytes())
	if err != nil {
		fmt.Printf("Expected failure: %v\n", err)
	} else {
		fmt.Println("Unexpected success: this should not decompress correctly")
	}

	// Case 2: Properly Close
	var buf2 bytes.Buffer
	zw2 := zstd.NewWriter(&buf2)
	_, err = zw2.Write(data)
	if err != nil {
		log.Fatalf("write (close case): %v", err)
	}
	err = zw2.Close() // Properly finalize frame
	if err != nil {
		log.Fatalf("close: %v", err)
	}

	// Attempt to decompress (should succeed)
	fmt.Println("\nTrying to decompress properly closed data:")
	out, err := zstd.Decompress(nil, buf2.Bytes())
	if err != nil {
		log.Fatalf("Unexpected decompression error: %v", err)
	}
	fmt.Printf("Decompressed OK: %s\n", string(out))
}
